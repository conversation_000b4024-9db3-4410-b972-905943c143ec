from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import EventTemplate, EventStep
from .serializers import EventTemplateSerializer, EventStepSerializer, EventStepCreateSerializer

class EventTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint for viewing and editing event templates.
    """
    queryset = EventTemplate.objects.all()
    serializer_class = EventTemplateSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return EventTemplate.objects.filter(creator=self.request.user)

    def perform_create(self, serializer):
        serializer.save(creator=self.request.user)
    
    @action(detail=True, methods=['post'], url_path='add-step')
    def add_step(self, request, pk=None):
        template = self.get_object()
        
        # --- FIX: Pass the template object to the serializer's context ---
        # The serializer will now handle the logic for creating the step.
        serializer = EventStepCreateSerializer(
            data=request.data,
            context={'template': template} # Pass template in context
        )
        
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class EventStepViewSet(viewsets.ModelViewSet):
    """
    API endpoint for individual steps (for future editing/deleting).
    """
    queryset = EventStep.objects.all()
    serializer_class = EventStepSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        return EventStep.objects.filter(template__creator=self.request.user)
