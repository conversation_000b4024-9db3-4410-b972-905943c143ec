from django.db import models
from django.conf import settings

class EventTemplate(models.Model):
    """
    Represents a user-created template for a sequence of events.
    e.g., "公司年会流程", "线上破冰环节"
    """
    name = models.CharField(max_length=100)
    description = models.TextField(blank=True)
    creator = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='event_templates')
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"'{self.name}' by {self.creator.username}"


class EventStep(models.Model):
    """
    Represents a single step within an EventTemplate.
    """
    # --- Step Type Choices ---
    # This list will grow as we add more features like polls, Q&A, etc.
    STEP_GAME_PICTIONARY = 'GAME_PICTIONARY'
    STEP_FREE_CHAT = 'FREE_CHAT'
    # Future step types: STEP_POLL, STEP_QNA, etc.
    
    STEP_TYPE_CHOICES = [
        (STEP_GAME_PICTIONARY, '游戏：你画我猜'),
        (STEP_FREE_CHAT, '自由讨论'),
    ]

    template = models.ForeignKey(EventTemplate, on_delete=models.CASCADE, related_name='steps')
    order = models.PositiveIntegerField() # The sequence of this step in the template
    step_type = models.CharField(max_length=50, choices=STEP_TYPE_CHOICES)
    
    # A flexible field to store settings for each step type.
    # e.g., for a game, it might store the number of rounds.
    # For a poll, it might store the question and options.
    configuration = models.JSONField(default=dict, blank=True)
    
    duration = models.PositiveIntegerField(default=300, help_text="Duration of this step in seconds")

    class Meta:
        # Steps should be ordered within a template
        ordering = ['template', 'order']

    def __str__(self):
        return f"Step {self.order}: {self.get_step_type_display()} for '{self.template.name}'"
