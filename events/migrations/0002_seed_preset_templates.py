# Generated by Django

from django.db import migrations
# --- Import the password hasher ---
from django.contrib.auth.hashers import make_password

def create_preset_templates(apps, schema_editor):
    """
    Creates the default system templates for users to choose from.
    """
    User = apps.get_model('core', 'User')
    EventTemplate = apps.get_model('events', 'EventTemplate')
    EventStep = apps.get_model('events', 'EventStep')

    # --- FIX: Set the hashed password directly during creation ---
    # Find or create a system user to be the "creator" of these templates
    system_user, created = User.objects.get_or_create(
        username='system', 
        defaults={
            'is_staff': True, 
            'is_superuser': True,
            # Use make_password to hash the password before saving
            'password': make_password('a_strong_system_password')
        }
    )

    # --- Preset 1: Pictionary Game ---
    pictionary_template, _ = EventTemplate.objects.get_or_create(
        name="经典：你画我猜",
        defaults={
            "description": "一局紧张刺激的你画我猜游戏，考验你和朋友的默契与画功。",
            "creator": system_user
        }
    )
    EventStep.objects.get_or_create(
        template=pictionary_template,
        order=1,
        defaults={
            "step_type": 'GAME_PICTIONARY',
            "duration": 300 # 5 minutes
        }
    )

    # --- Preset 2: Free Chat ---
    chat_template, _ = EventTemplate.objects.get_or_create(
        name="轻松：自由聊天室",
        defaults={
            "description": "没有游戏，没有压力，只是一个纯粹的聊天空间。",
            "creator": system_user
        }
    )
    EventStep.objects.get_or_create(
        template=chat_template,
        order=1,
        defaults={
            "step_type": 'FREE_CHAT',
            "duration": 3600 # 1 hour
        }
    )

class Migration(migrations.Migration):

    dependencies = [
        ('events', '0001_initial'),
        ('core', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_preset_templates),
    ]
