from rest_framework import serializers
from django.db.models import Max
from .models import EventTemplate, EventStep

class EventStepSerializer(serializers.ModelSerializer):
    class Meta:
        model = EventStep
        fields = ['id', 'order', 'step_type', 'configuration', 'duration']

class EventStepCreateSerializer(serializers.ModelSerializer):
    """
    Serializer specifically for creating a new EventStep.
    The 'order' field is now handled automatically within this serializer.
    """
    # 'order' is now a read-only field, as it's calculated on the backend.
    order = serializers.IntegerField(read_only=True)

    class Meta:
        model = EventStep
        # The client only needs to provide these fields.
        # 'template' will be passed via context from the view.
        fields = ['step_type', 'duration', 'configuration', 'order']

    def create(self, validated_data):
        # --- FIX: Calculate the order automatically upon creation ---
        template = self.context['template']
        
        # Determine the order for the new step
        last_step = template.steps.aggregate(Max('order'))
        new_order = (last_step['order__max'] or 0) + 1
        
        # Add the calculated order and the template to the instance
        validated_data['order'] = new_order
        validated_data['template'] = template
        
        return super().create(validated_data)


class EventTemplateSerializer(serializers.ModelSerializer):
    steps = EventStepSerializer(many=True, read_only=True)
    creator_username = serializers.ReadOnlyField(source='creator.username')

    class Meta:
        model = EventTemplate
        fields = ['id', 'name', 'description', 'creator_username', 'created_at', 'steps']
