# Generated by Django 5.2.3 on 2025-06-30 11:45

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('core', '0002_room_status'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Game',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('game_type', models.CharField(choices=[('PICTIONARY', 'Pictionary')], max_length=20)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('room', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='game', to='core.room')),
            ],
        ),
        migrations.CreateModel(
            name='PictionaryGame',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('current_word', models.CharField(max_length=100)),
                ('round_start_time', models.DateTimeField(auto_now=True)),
                ('current_drawer', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='drawing_games', to=settings.AUTH_USER_MODEL)),
                ('game', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='pictionary_data', to='games.game')),
            ],
        ),
    ]
