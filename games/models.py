from django.db import models
from django.conf import settings
from core.models import Room

class Game(models.Model):
    """
    Represents a single game session within a room.
    """
    GAME_PICTIONARY = 'PICTIONARY'
    GAME_CHOICES = [
        (GAME_PICTIONARY, 'Pictionary'),
    ]

    room = models.OneToOneField(Room, on_delete=models.CASCADE, related_name='game')
    game_type = models.CharField(max_length=20, choices=GAME_CHOICES)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.get_game_type_display()} in Room {self.room.room_code}"


class PictionaryGame(models.Model):
    """
    Stores the specific state for a "Pictionary" game session.
    """
    game = models.OneToOneField(Game, on_delete=models.CASCADE, related_name='pictionary_data')
    current_word = models.CharField(max_length=100)
    current_drawer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.SET_NULL, null=True, related_name='drawing_games')
    round_start_time = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Pictionary for {self.game}"

# --- ADD THIS NEW MODEL ---
class PlayerScore(models.Model):
    """
    Tracks the score for a player within a specific room.
    """
    room = models.ForeignKey(Room, on_delete=models.CASCADE, related_name='scores')
    player = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    score = models.IntegerField(default=0)

    class Meta:
        # Ensure a player has only one score entry per room
        unique_together = ('room', 'player')

    def __str__(self):
        return f"{self.player.username}: {self.score} in Room {self.room.room_code}"
