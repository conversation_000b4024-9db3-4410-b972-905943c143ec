# Tuan<PERSON>_Backend/asgi.py

import os
import django
from channels.routing import ProtocolType<PERSON>outer, URLRouter
from django.core.asgi import get_asgi_application

# Explicitly set up Django before importing anything else
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'Tuanzi_Backend.settings')
django.setup()

# Now that Django is set up, we can safely import other components
from core.middleware import TokenAuthMiddleware
import core.routing

# This is the simplified and more robust middleware stack.
# Our custom TokenAuthMiddleware directly wraps the URLRouter.
application = ProtocolTypeRouter({
    "http": get_asgi_application(),
    "websocket": TokenAuthMiddleware(
        URLRouter(
            core.routing.websocket_urlpatterns
        )
    ),
})
