# Tuanzi 后端服务

## 1. 概述

本项目后端使用Django构建，通过Django REST Framework提供HTTP API，并通过Django Channels和Daphne服务器提供实时的WebSocket通信能力。

## 2. 环境设置

**前置要求**:
- Python 3.10+
- `pip` 和 `venv` (或 `conda`)
- Redis (用于Channels的后台通信层)

**安装步骤**:

1. **克隆仓库**

2. **创建并激活Python虚拟环境**:
   ```bash
   python -m venv venv
   source venv/bin/activate  # macOS/Linux
   # venv\\Scripts\\activate  # Windows
   ```

3. **安装依赖**:
   ```bash
   pip install -r requirements.txt
   ```
   *(注意: 请开发者A使用 `pip freeze > requirements.txt` 生成最新的依赖文件)*

4. **数据库迁移**:
   ```bash
   python manage.py migrate
   ```
   此命令会创建所有数据表，并填充预设的环节模板（你画我猜/自由聊天）。

## 3. 运行开发服务器

**重要**: 由于项目使用了WebSocket，我们**必须**使用ASGI服务器`Daphne`来运行，而不是`runserver`。

```bash
daphne -b 0.0.0.0 -p 8000 Tuanzi_Backend.asgi:application
```
- `-b 0.0.0.0`: 绑定到所有网络接口，允许局域网内的手机App访问。
- `-p 8000`: 在8000端口运行。

## 4. 项目架构

后端由以下几个核心Django App组成：

- `core`: 负责核心的用户模型、房间模型以及WebSocket的消费者(`RoomConsumer`)。这是整个实时通信和流程控制的中枢。
- `games`: 负责所有具体游戏的模型和逻辑。目前包含了`PictionaryGame`和`PlayerScore`。
- `events`: 负责“环节”系统的模型和API。包含了`EventTemplate`（环节模板）和`EventStep`（环节步骤）。

## 5. 关键API端点

- `/api/token/`: 用户登录，获取JWT。
- `/api/register/`: 用户注册。
- `/api/rooms/create/`: (POST) 基于一个`template_id`创建新房间。
- `/api/rooms/join/`: (POST) 基于`room_code`加入房间。
- `/api/events/templates/`: (GET, POST) 获取用户自己的模板列表，或创建新模板。
- `/api/events/templates/{id}/add-step/`: (POST) 为指定模板添加一个新步骤。
- `/api/events/steps/{id}/`: (DELETE) 删除一个步骤。

## 6. WebSocket通信

- **入口**: `ws://<your_ip>:8000/ws/room/{room_code}/?token=<jwt_token>`
- **核心处理器**: `core/consumers.py`中的`RoomConsumer`。
- **核心逻辑**:
  - 采用“动作-载荷”(`action-payload`)模式进行通信。
  - `RoomConsumer`作为“环节执行器”，通过`next_step`动作来驱动流程。
  - 根据当前环节的类型，将具体游戏逻辑分发给不同的处理器或广播不同的消息。