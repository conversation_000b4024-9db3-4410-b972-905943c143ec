# 模块扩展指南

欢迎为团子项目贡献新的功能！本指南将引导您如何为应用添加一个新的活动环节（例如，一款新游戏）。

## 核心架构理念

我们的应用是一个**“环节执行器”**。房间本身不包含具体逻辑，它只负责按顺序执行一个“环节模板”(`EventTemplate`)中的所有“环节步骤”(`EventStep`)。

因此，添加一个新功能的核心，就是定义一个新的`EventStep`类型，并为其编写相应的后端处理逻辑和前端UI组件。

## 示例：添加一款新游戏“狼人杀”

假设我们要添加一款新游戏“狼人杀”。

### 第1步：后端开发

1.  **定义数据模型 (`games/models.py`)**:
    在`games/models.py`中为新游戏创建一个模型，用来存储其特有的状态。
    ```python
    class WerewolfGame(models.Model):
        game = models.OneToOneField(Game, on_delete=models.CASCADE)
        words = models.JSONField() # e.g., {"civilian": "苹果", "spy": "香蕉"}
        # ... 其他字段，如投票结果、出局玩家等
    ```

2.  **定义环节类型 (`events/models.py`)**:
    在`EventStep`模型的`STEP_TYPE_CHOICES`中添加一个新的常量和选项。
    ```python
    class EventStep(models.Model):
        STEP_GAME_WEREWOLF = 'GAME_WEREWOLF'
        STEP_TYPE_CHOICES = [
            (STEP_GAME_PICTIONARY, '游戏：你画我猜'),
            (STEP_FREE_CHAT, '自由讨论'),
            (STEP_GAME_WEREWOLF, '游戏：狼人杀'), # <-- 新增，自定义
        ]
        # ...
    ```

3.  **实现游戏开始逻辑 (`core/consumers.py`)**:
    在`consumers.py`中创建一个新的数据库辅助函数`start_werewolf_for_step`，负责初始化游戏数据。

4.  **在环节执行器中注册新游戏 (`core/consumers.py`)**:
    在`RoomConsumer`的`handle_next_step`方法中，添加一个新的`elif`分支来处理新环节类型。
    ```python
    async def handle_next_step(self):
        # ...
        if next_step.step_type == EventStep.STEP_GAME_PICTIONARY:
            # ...
        elif next_step.step_type == EventStep.STEP_GAME_WEREWOLF: # <-- 新增
            game_data, error = await start_werewolf_for_step(room, next_step)
            # ... 广播 'step_started'
    ```

5.  **实现游戏内的WebSocket通信 (`core/consumers.py`)**:
    在`RoomConsumer`的`receive`方法中，为新游戏添加专属的`action`处理器（如`handle_vote`, `handle_reveal`），并为其编写相应的广播逻辑。

### 第2步：前端开发

1.  **创建环节UI组件 (`src/components/steps/`)**:
    创建一个新的React组件，例如`WerewolfView.tsx`。这个组件将负责渲染“狼人杀”的游戏界面，并接收所有必要的状态作为props。

2.  **在`RoomScreen`中注册新组件 (`src/screens/RoomScreen.tsx`)**:
    在`RoomScreen`的`renderCurrentStep`方法中，为新的环节类型添加一个`case`。
    ```typescript
    const renderCurrentStep = () => {
        // ...
        switch (currentStep.step_type) {
            case 'GAME_PICTIONARY':
                return <PictionaryView ... />;
            case 'GAME_WEREWOLF': // <-- 新增
                return <WerewolfView ... />;
            // ...
        }
    }
    ```

3.  **在`RoomScreen`中处理新状态 (`src/screens/RoomScreen.tsx`)**:
    在`RoomScreen`的`onmessage`处理器中，添加对新游戏专属WebSocket消息的处理逻辑，并更新相应的状态，然后将这些状态通过props传递给`WerewolfView`。

4.  **在“添加步骤”页面中提供选项 (`src/screens/EventDesigner/AddStepScreen.tsx`)**:
    最后，更新`AddStepScreen.tsx`中的`STEP_CHOICES`常量，让用户可以在设计器中选择“游戏：狼人杀”这个新环节。

通过遵循这个模式，我们可以轻松地为应用添加任意数量的新游戏和新活动，而无需对核心的房间和流程控制逻辑进行大规模修改。
