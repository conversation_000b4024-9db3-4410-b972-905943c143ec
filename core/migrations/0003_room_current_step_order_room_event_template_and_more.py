# Generated by Django 5.2.3 on 2025-07-02 18:16

import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('core', '0002_room_status'),
        ('events', '0002_seed_preset_templates'),
    ]

    operations = [
        migrations.AddField(
            model_name='room',
            name='current_step_order',
            field=models.PositiveIntegerField(default=0),
        ),
        migrations.AddField(
            model_name='room',
            name='event_template',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='events.eventtemplate'),
        ),
        migrations.AlterField(
            model_name='room',
            name='status',
            field=models.CharField(choices=[('WAITING', 'Waiting for players'), ('IN_PROGRESS', 'In Progress'), ('FINISHED', 'Finished')], default='WAITING', max_length=20),
        ),
    ]
