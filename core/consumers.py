import json
import random
import logging
import asyncio
from channels.generic.websocket import AsyncWebsocketConsumer
from channels.db import database_sync_to_async
from .models import Room
from events.models import EventStep
from games.models import Game, PictionaryGame, PlayerScore
from games.words import WORD_LIST

logger = logging.getLogger(__name__)

# All @database_sync_to_async helper functions remain the same as the last complete version.
@database_sync_to_async
def update_scores(winner, drawer, room):
    winner_score, _ = PlayerScore.objects.get_or_create(room=room, player=winner)
    winner_score.score += 10; winner_score.save()
    drawer_score, _ = PlayerScore.objects.get_or_create(room=room, player=drawer)
    drawer_score.score += 5; drawer_score.save()
    scores = PlayerScore.objects.filter(room=room).order_by('-score')
    return {score.player.username: score.score for score in scores}

@database_sync_to_async
def get_pictionary_game(room_code):
    try:
        return PictionaryGame.objects.select_related('game__room', 'current_drawer').get(game__room__room_code=room_code, game__is_active=True)
    except PictionaryGame.DoesNotExist:
        return None

@database_sync_to_async
def end_pictionary_round(room_code):
    try:
        room = Room.objects.get(room_code=room_code)
        if room.status == Room.STATUS_IN_PROGRESS:
            room.status = Room.STATUS_WAITING
            room.save()
            if hasattr(room, 'game'):
                room.game.is_active = False
                room.game.save()
            return room.status
        return None
    except Room.DoesNotExist:
        return None

@database_sync_to_async
def get_room_with_template(room_code):
    """Get room with its event template and participants."""
    try:
        return Room.objects.select_related('event_template').prefetch_related('participants').get(room_code=room_code)
    except Room.DoesNotExist:
        return None

def advance_to_next_step_sync(room):
    """Advance room to the next step in its event template. (Sync version)"""
    if not room.event_template:
        return None

    # Get the next step based on current_step_order
    next_step = room.event_template.steps.filter(order__gt=room.current_step_order).order_by('order').first()

    if next_step:
        # Update room's current step order
        room.current_step_order = next_step.order
        room.save()
        return next_step

    return None  # No more steps

# Async wrapper
advance_to_next_step = database_sync_to_async(advance_to_next_step_sync)

@database_sync_to_async
def save_room(room):
    """Save room instance to database."""
    room.save()

@database_sync_to_async
def start_pictionary_for_step(room, step):
    participants = list(room.participants.all())
    if len(participants) < 1:
        return None, "至少需要1名玩家才能开始游戏。"
    drawer = random.choice(participants)
    word = random.choice(WORD_LIST)
    room.status = Room.STATUS_IN_PROGRESS
    room.save()
    game_session, _ = Game.objects.update_or_create(room=room, defaults={'game_type': Game.GAME_PICTIONARY, 'is_active': True})
    PictionaryGame.objects.update_or_create(game=game_session, defaults={'current_word': word, 'current_drawer': drawer})
    return {
        "drawer": drawer.username,
        "word": word,
        "duration": step.duration,  # Include step duration
        "room_status": room.status,
        "step_info": {"step_type": step.step_type, "order": step.order}
    }, None


class RoomConsumer(AsyncWebsocketConsumer):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.timer_task = None  # Store the timer task for cancellation

    async def connect(self):
        if not self.scope['user'] or not self.scope['user'].is_authenticated:
            logger.warning(f"Unauthenticated connection attempt")
            await self.close()
            return

        self.room_code = self.scope['url_route']['kwargs']['room_code']
        self.room_group_name = f'room_{self.room_code}'

        try:
            await self.channel_layer.group_add(self.room_group_name, self.channel_name)
            await self.accept()
            logger.info(f"User {self.scope['user'].username} connected to room {self.room_code}")
        except Exception as e:
            logger.error(f"Error connecting user to room {self.room_code}: {e}")
            await self.close()

    async def disconnect(self, _close_code):
        # Cancel any running timer
        if self.timer_task and not self.timer_task.done():
            self.timer_task.cancel()

        if hasattr(self, 'room_group_name'):
            try:
                await self.channel_layer.group_discard(self.room_group_name, self.channel_name)
                logger.info(f"User {self.scope['user'].username} disconnected from room {self.room_code}")
            except Exception as e:
                logger.error(f"Error disconnecting user from room {self.room_code}: {e}")

    async def receive(self, text_data):
        try:
            data = json.loads(text_data)
            action = data.get('action')
            payload = data.get('payload', {})

            logger.debug(f"Received action '{action}' from user {self.scope['user'].username} in room {self.room_code}")

            if action == 'next_step':
                await self.handle_next_step()
            elif action == 'send_message':
                await self.handle_chat_message(payload)
            elif action == 'send_drawing':
                await self.handle_drawing_data(payload)
            elif action == 'restart_game':
                await self.handle_restart_game(payload)
            else:
                logger.warning(f"Unknown action '{action}' from user {self.scope['user'].username}")
                await self.send_error(f"Unknown action: {action}")
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON received from user {self.scope['user'].username}: {e}")
            await self.send_error("Invalid message format")
        except Exception as e:
            logger.error(f"Error processing message from user {self.scope['user'].username}: {e}")
            await self.send_error("Internal server error")
    async def handle_next_step(self):
        try:
            user = self.scope['user']
            logger.info(f"User {user.username} requesting next step in room {self.room_code}")

            room = await get_room_with_template(self.room_code)

            if not room:
                logger.error(f"Room {self.room_code} not found")
                await self.send_error('房间不存在。')
                return

            # Get host information safely
            room_host = await database_sync_to_async(lambda: room.host)()
            logger.info(f"Room found: {room.room_code}, host: {room_host}, current_step_order: {room.current_step_order}")

            if room_host != user:
                logger.warning(f"User {user.username} tried to start next step but is not host of room {self.room_code}")
                await self.send_error('只有房主才能开始下一环节。')
                return

            logger.info(f"User {user.username} is confirmed as host, advancing to next step")
            next_step = await advance_to_next_step(room)

            if not next_step:
                logger.info(f"All steps completed for room {self.room_code}")
                await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_event_over'})
                return

            logger.info(f"Starting step {next_step.order} ({next_step.step_type}) in room {self.room_code}")

            if next_step.step_type == EventStep.STEP_GAME_PICTIONARY:
                logger.info(f"Starting pictionary game for room {self.room_code}")
                game_data, error = await start_pictionary_for_step(room, next_step)
                if error:
                    logger.error(f"Failed to start pictionary game in room {self.room_code}: {error}")
                    await self.send_error(error)
                    return
                logger.info(f"Pictionary game started successfully for room {self.room_code}")
                await self.channel_layer.group_send(self.room_group_name, {'type': 'broadcast_step_start', 'payload': game_data})

                # Start timer for this step
                await self.start_step_timer(next_step.duration)
            elif next_step.step_type == EventStep.STEP_FREE_CHAT:
                logger.info(f"Starting free chat for room {self.room_code}")
                room.status = Room.STATUS_IN_PROGRESS
                await database_sync_to_async(room.save)()
                await self.channel_layer.group_send(self.room_group_name, {
                    'type': 'broadcast_step_start',
                    'payload': {
                        "room_status": room.status,
                        "step_info": {"step_type": next_step.step_type, "order": next_step.order}
                    }
                })
            else:
                logger.error(f"Unknown step type {next_step.step_type} in room {self.room_code}")
                await self.send_error(f"不支持的环节类型: {next_step.step_type}")

        except Exception as e:
            logger.error(f"Error handling next_step in room {self.room_code}: {e}", exc_info=True)
            await self.send_error("处理下一环节时发生错误。")
    async def handle_chat_message(self, payload):
        try:
            message = payload.get('message', '').strip()
            user = self.scope['user']

            if not message:
                logger.warning(f"Empty message from user {user.username} in room {self.room_code}")
                return

            # Check if this is a pictionary game and if the message is a correct guess
            game = await get_pictionary_game(self.room_code)
            if game:
                # Validate game state
                if not game.current_word or not game.current_drawer:
                    logger.error(f"Invalid game state in room {self.room_code}: missing word or drawer")
                    await self.send_error("游戏状态异常，请重新开始。")
                    return

                # Check if user is the drawer (drawers can't guess)
                if user.id == game.current_drawer.id:
                    logger.debug(f"Drawer {user.username} tried to send message in room {self.room_code}")
                    return  # Silently ignore messages from drawer

                # Check if message is the correct answer
                if message.lower() == game.current_word.lower():
                    logger.info(f"User {user.username} guessed correctly in room {self.room_code}: {game.current_word}")
                    updated_scores = await update_scores(winner=user, drawer=game.current_drawer, room=game.game.room)
                    new_room_status = await end_pictionary_round(self.room_code)

                    if new_room_status:
                        await self.channel_layer.group_send(self.room_group_name, {
                            'type': 'broadcast_round_over',
                            'payload': {
                                'winner': user.username,
                                'word': game.current_word,
                                'room_status': new_room_status,
                                'scores': updated_scores,
                            }
                        })
                    return

            # Regular chat message
            await self.channel_layer.group_send(self.room_group_name, {
                'type': 'broadcast_chat_message',
                'payload': {'message': message, 'sender': user.username}
            })

        except Exception as e:
            logger.error(f"Error handling chat message from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("发送消息时发生错误。")
    async def handle_drawing_data(self, payload):
        try:
            user = self.scope['user']
            path_data = payload.get('path_data')

            if not path_data:
                logger.warning(f"Empty drawing data from user {user.username} in room {self.room_code}")
                return

            # Validate that user is allowed to draw
            game = await get_pictionary_game(self.room_code)
            if game:
                if user.id != game.current_drawer.id:
                    logger.warning(f"Non-drawer {user.username} tried to draw in room {self.room_code}")
                    await self.send_error("只有绘画者可以绘画。")
                    return

            # Broadcast drawing data to all users
            await self.channel_layer.group_send(self.room_group_name, {
                'type': 'broadcast_drawing_data',
                'payload': {'path_data': path_data}
            })

        except Exception as e:
            logger.error(f"Error handling drawing data from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("处理绘图数据时发生错误。")

    async def handle_restart_game(self, payload):
        """Handle request to restart a game."""
        try:
            user = self.scope['user']
            game_type = payload.get('game_type')

            logger.info(f"User {user.username} requesting restart of {game_type} in room {self.room_code}")

            room = await get_room_with_template(self.room_code)
            if not room:
                await self.send_error('房间不存在。')
                return

            # Check if user is host
            room_host = await database_sync_to_async(lambda: room.host)()
            if room_host != user:
                await self.send_error('只有房主才能重新开始游戏。')
                return

            # Get current step to use its duration
            current_step = await database_sync_to_async(
                lambda: room.event_template.steps.filter(order=room.current_step_order).first()
            )()

            if not current_step:
                await self.send_error('无法找到当前环节信息。')
                return

            if game_type == 'PICTIONARY':
                # Restart pictionary game
                game_data, error = await start_pictionary_for_step(room, current_step)
                if error:
                    await self.send_error(error)
                    return

                await self.channel_layer.group_send(self.room_group_name, {
                    'type': 'broadcast_step_start',
                    'payload': game_data
                })

                # Start timer for the restarted game
                await self.start_step_timer(current_step.duration)

            else:
                await self.send_error(f"不支持重新开始游戏类型: {game_type}")

        except Exception as e:
            logger.error(f"Error handling restart game from user {user.username} in room {self.room_code}: {e}")
            await self.send_error("重新开始游戏时发生错误。")

    async def start_step_timer(self, duration_seconds):
        """Start a timer for the current step."""
        # Cancel any existing timer
        if self.timer_task and not self.timer_task.done():
            self.timer_task.cancel()

        # Start new timer
        self.timer_task = asyncio.create_task(self._step_timer(duration_seconds))

    async def _step_timer(self, duration_seconds):
        """Timer coroutine that handles step timeout."""
        try:
            await asyncio.sleep(duration_seconds)
            # Time's up - end the current step
            await self.handle_step_timeout()
        except asyncio.CancelledError:
            logger.info(f"Timer cancelled for room {self.room_code}")
            raise
        except Exception as e:
            logger.error(f"Error in step timer for room {self.room_code}: {e}")

    async def handle_step_timeout(self):
        """Handle when a step times out."""
        try:
            logger.info(f"Step timeout for room {self.room_code}")

            # Check if this is a pictionary game and end it
            game = await get_pictionary_game(self.room_code)
            if game:
                new_room_status = await end_pictionary_round(self.room_code)
                if new_room_status:
                    await self.channel_layer.group_send(self.room_group_name, {
                        'type': 'broadcast_round_over',
                        'payload': {
                            'winner': None,  # No winner - time's up
                            'word': game.current_word,
                            'room_status': new_room_status,
                            'scores': {},  # No score update for timeout
                            'timeout': True,  # Indicate this was a timeout
                        }
                    })
                    return

            # For other step types, just end the step
            room = await get_room_with_template(self.room_code)
            if room:
                room.status = Room.STATUS_WAITING
                await save_room(room)
                await self.channel_layer.group_send(self.room_group_name, {
                    'type': 'broadcast_step_timeout',
                    'payload': {'room_status': room.status}
                })

        except Exception as e:
            logger.error(f"Error handling step timeout for room {self.room_code}: {e}")

    # --- NEW: Generic error handler ---
    async def send_error(self, message):
        """Sends an error message back to the originating client."""
        await self.send(text_data=json.dumps({'type': 'error', 'payload': {'message': message}}))

    # --- BROADCAST HANDLERS ---
    async def broadcast_step_start(self, event):
        # --- FIX: Create a copy of the payload for each user ---
        payload = event['payload'].copy()
        step_type = payload.get('step_info', {}).get('step_type')
        if step_type == EventStep.STEP_GAME_PICTIONARY:
            if self.scope['user'].username != payload['drawer']:
                # Hide the word for non-drawers by replacing each character with underscore
                word = payload['word']
                payload['word'] = " ".join(["_" for _ in word if _ != ' '])
        await self.send(text_data=json.dumps({'type': 'step_started', 'payload': payload}))

    async def broadcast_chat_message(self, event): await self.send(text_data=json.dumps({'type': 'chat_message', 'payload': event['payload']}))
    async def broadcast_drawing_data(self, event): await self.send(text_data=json.dumps({'type': 'drawing_data', 'payload': event['payload']}))
    async def broadcast_round_over(self, event): await self.send(text_data=json.dumps({'type': 'round_over', 'payload': event['payload']}))
    async def broadcast_step_timeout(self, event): await self.send(text_data=json.dumps({'type': 'step_timeout', 'payload': event['payload']}))
    async def broadcast_event_over(self, _event): await self.send(text_data=json.dumps({'type': 'event_finished', 'payload': {'message': '所有环节已结束！感谢您的参与。'}}))
