// src/types.ts

// --- Data Structure Types ---

export type Room = {
  id: number;
  room_code: string;
  host: string;
  participants: string[];
  status: string;
};

export type Message = {
  sender: string;
  message: string;
};

export type EventStep = {
    id: number;
    order: number;
    step_type: string; // The correct property name is 'step_type'
    configuration: any;
    duration: number;
};

export type EventTemplate = {
    id: number;
    name: string;
    description: string;
    creator_username: string;
    created_at: string;
    steps: EventStep[];
};


// --- Navigation Param List ---
// This defines all possible screens and the parameters they expect.

export type RootStackParamList = {
  Home: undefined;
  Register: undefined;
  Login: undefined;
  CreateRoom: undefined;
  Room: { room: Room };
  EventDesigner: undefined;
  CreateTemplate: undefined;
  TemplateDetail: { templateId: number };
  AddStep: { templateId: number };
};
