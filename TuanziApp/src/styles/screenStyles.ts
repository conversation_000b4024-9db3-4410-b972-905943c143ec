import { StyleSheet } from 'react-native';
import { colors } from './commonStyles';

export const homeScreenStyles = StyleSheet.create({
    joinSection: {
        width: '100%',
        marginTop: 40,
        alignItems: 'center',
        padding: 20,
        borderTopWidth: 1,
        borderTopColor: colors.lightGray,
    },
    logoutButton: {
        position: 'absolute',
        bottom: 40,
    },
});

export const roomScreenStyles = StyleSheet.create({
    container: { 
        flex: 1, 
        backgroundColor: colors.background 
    },
    header: {
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: colors.lightGray,
        alignItems: 'center',
    },
    hostControls: { 
        marginVertical: 10, 
        alignItems: 'center' 
    },
    chatList: { 
        flex: 1, 
        paddingHorizontal: 10 
    },
    inputArea: { 
        flexDirection: 'row', 
        padding: 10, 
        borderTopWidth: 1, 
        borderColor: colors.mediumGray,
        backgroundColor: colors.background,
    },
    chatInput: { 
        flex: 1, 
        height: 40, 
        borderWidth: 1, 
        borderColor: colors.mediumGray, 
        borderRadius: 20, 
        paddingHorizontal: 15, 
        marginRight: 10,
        backgroundColor: colors.lightGray,
    },
    myMessageBubble: { 
        backgroundColor: colors.myMessageBubble, 
        padding: 12, 
        borderRadius: 18, 
        marginVertical: 5, 
        maxWidth: '80%', 
        alignSelf: 'flex-end', 
        marginRight: 10 
    },
    theirMessageBubble: { 
        backgroundColor: colors.lightGray, 
        padding: 12, 
        borderRadius: 18, 
        marginVertical: 5, 
        maxWidth: '80%', 
        alignSelf: 'flex-start', 
        marginLeft: 10 
    },
    messageSender: { 
        fontWeight: 'bold', 
        marginBottom: 4,
        color: colors.primary,
    },
    messageText: { 
        fontSize: 16, 
        color: colors.text 
    },
    gameContainer: { 
        flex: 1, 
        width: '100%', 
        alignItems: 'center', 
        paddingBottom: 10 
    },
    topInfoPanel: { 
        flexDirection: 'row', 
        justifyContent: 'space-around', 
        width: '100%', 
        paddingVertical: 10 
    },
    infoText: { 
        fontSize: 16,
        color: colors.darkGray,
    },
    wordPanel: { 
        marginVertical: 15, 
        alignItems: 'center' 
    },
    wordText: { 
        fontSize: 36, 
        fontWeight: 'bold', 
        letterSpacing: 10,
        color: colors.text,
    },
    roleText: { 
        fontSize: 14, 
        color: colors.darkGray, 
        marginTop: 5 
    },
    subtitle: {
        fontSize: 22,
        fontWeight: '600',
        marginVertical: 15,
        textAlign: 'center',
    }
});
