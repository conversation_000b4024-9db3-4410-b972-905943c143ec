import { StyleSheet } from 'react-native';

export const colors = {
    primary: '#007AFF',
    background: '#fff',
    text: '#000',
    lightGray: '#f0f0f0',
    mediumGray: '#ccc',
    darkGray: '#888',
    white: '#fff',
    black: '#000',
    myMessageBubble: '#dcf8c6',
    error: 'red',
    success: '#4CAF50',
};

export const commonStyles = StyleSheet.create({
    safeArea: {
        flex: 1,
        backgroundColor: colors.background,
    },
    container: {
        flex: 1,
        backgroundColor: colors.background,
        alignItems: 'center',
        justifyContent: 'center',
        padding: 20,
    },
    title: {
        fontSize: 28,
        fontWeight: 'bold',
        marginBottom: 24,
        color: colors.text,
    },
    subtitle: {
        fontSize: 20,
        fontWeight: '600',
        marginTop: 20,
        marginBottom: 10,
        color: colors.darkGray,
    },
    input: {
        width: '90%',
        height: 44,
        borderColor: colors.mediumGray,
        borderWidth: 1,
        borderRadius: 8,
        marginBottom: 12,
        paddingHorizontal: 15,
        fontSize: 16,
    },
    buttonContainer: {
        marginTop: 10,
        width: '90%',
    },
    linkText: {
        color: colors.primary,
        marginTop: 20,
        fontSize: 16,
    },
});
