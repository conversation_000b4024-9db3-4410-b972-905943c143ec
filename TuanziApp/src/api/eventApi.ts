import { API_URL } from './client';
import { EventTemplate } from '../types';

export const getEventTemplates = async (token: string): Promise<EventTemplate[]> => {
    const response = await fetch(`${API_URL}/api/events/templates/`, {
        headers: {
            'Authorization': `Bearer ${token}`,
        },
    });
    if (!response.ok) {
        throw new Error('Failed to fetch event templates');
    }
    return response.json();
};

export const createEventTemplate = async (token: string, name: string, description: string): Promise<EventTemplate> => {
    const response = await fetch(`${API_URL}/api/events/templates/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({ name, description }),
    });
    if (!response.ok) {
        throw new Error('Failed to create event template');
    }
    return response.json();
};

// --- NEW: Function to get details of a single template ---
export const getTemplateDetails = async (token: string, templateId: number): Promise<EventTemplate> => {
    const response = await fetch(`${API_URL}/api/events/templates/${templateId}/`, {
        headers: {
            'Authorization': `Bearer ${token}`,
        },
    });
    if (!response.ok) {
        throw new Error('Failed to fetch template details');
    }
    return response.json();
};
export const addStepToTemplate = async (token: string, templateId: number, stepData: { step_type: string, duration: number }) => {
    const response = await fetch(`${API_URL}/api/events/templates/${templateId}/add-step/`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify(stepData),
    });
    if (!response.ok) {
        const errorData = await response.json();
        throw new Error(JSON.stringify(errorData));
    }
    return response.json();
};
