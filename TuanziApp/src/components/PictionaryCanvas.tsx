import React, { useState, useRef } from 'react';
import { View, StyleSheet, PanResponder } from 'react-native';
import Svg, { Path } from 'react-native-svg';

// Type definition for a single continuous line drawn by the user
export interface PathData {
  id: string;
  path: string;
  color: string;
}

// Props interface for the PictionaryCanvas component
interface PictionaryCanvasProps {
  isDrawer: boolean;
  onDraw: (pathData: PathData) => void;
  paths: PathData[];
}

export const PictionaryCanvas: React.FC<PictionaryCanvasProps> = ({
  isDrawer,
  onDraw,
  paths,
}) => {
  // State to hold the path currently being drawn in real-time
  const [currentPath, setCurrentPath] = useState<PathData | null>(null);
  // A ref to the canvas's container View to measure its position
  const viewRef = useRef<View>(null);

  // PanResponder handles all the touch gestures on the canvas
  const panResponder = PanResponder.create({
    // Ask to be the responder when a touch starts
    onStartShouldSetPanResponder: () => isDrawer,
    onMoveShouldSetPanResponder: () => isDrawer,

    // When a touch gesture starts on the canvas
    onPanResponderGrant: (e, gestureState) => {
      // Measure the absolute position (pageX, pageY) of the canvas on the screen
      viewRef.current?.measure((x, y, width, height, pageX, pageY) => {
        // Calculate the precise starting point of the touch within the canvas
        const adjustedX = gestureState.x0 - pageX;
        const adjustedY = gestureState.y0 - pageY;
        // Start a new path at the adjusted coordinates
        setCurrentPath({
          id: Date.now().toString(), // Simple unique ID for the new path
          path: `M${adjustedX},${adjustedY}`,
          color: 'black',
        });
      });
    },

    // When the finger moves across the screen
    onPanResponderMove: (e, gestureState) => {
      // Measure again to be safe, though grant's values are usually sufficient
      viewRef.current?.measure((x, y, width, height, pageX, pageY) => {
        if (currentPath) {
          // Calculate the new point's position within the canvas
          const adjustedX = gestureState.moveX - pageX;
          const adjustedY = gestureState.moveY - pageY;
          // Append the new point to the current path's SVG string
          setCurrentPath(prevPath => ({
            ...prevPath!,
            path: `${prevPath!.path} L${adjustedX},${adjustedY}`,
          }));
        }
      });
    },

    // When the touch gesture is released from the screen
    onPanResponderRelease: () => {
      // The path is complete. Send it to the parent component to be broadcasted.
      if (currentPath) {
        onDraw(currentPath);
        // Clear the current path, as it's now part of the main `paths` array
        setCurrentPath(null);
      }
    },
  });

  // Combine the paths received from the server with the path currently being drawn
  const allPaths = currentPath ? [...paths, currentPath] : paths;

  return (
    // Attach the ref and PanResponder handlers to the container View
    <View ref={viewRef} style={styles.container} {...panResponder.panHandlers}>
      <Svg width="100%" height="100%">
        {/* Render all paths */}
        {allPaths.map(p => (
          <Path
            key={p.id}
            d={p.path}
            stroke={p.color}
            strokeWidth={3}
            fill="none"
          />
        ))}
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    width: '100%',
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 10,
  },
});
