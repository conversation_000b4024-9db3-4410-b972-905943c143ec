import React, { useState } from 'react';
import { View, Text, Button, StyleSheet, Alert, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useRoute, RouteProp, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { addStepToTemplate } from '../../api/eventApi';
import { RootStackParamList } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type AddStepRouteProp = RouteProp<RootStackParamList, 'AddStep'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'AddStep'>;

const STEP_CHOICES = [
    { label: '游戏：你画我猜', value: 'GAME_PICTIONARY' },
    { label: '自由讨论', value: 'FREE_CHAT' },
];

export const AddStepScreen = () => {
    const route = useRoute<AddStepRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const { templateId } = route.params;
    const { token } = useAuth();
    
    const [selectedStep, setSelectedStep] = useState<string | null>(null);
    const [isLoading, setIsLoading] = useState(false);

    const handleSave = async () => {
        if (!selectedStep) {
            Alert.alert('错误', '请选择一个环节类型。');
            return;
        }
        if (!token) return;

        setIsLoading(true);
        try {
            await addStepToTemplate(token, templateId, {
                step_type: selectedStep,
                duration: 300,
            });
            Alert.alert('成功', '新步骤已添加！');
            navigation.goBack();
        } catch (error) {
            console.error(error);
            Alert.alert('错误', '添加步骤失败。');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <View style={commonStyles.container}>
            <Text style={commonStyles.title}>选择环节类型</Text>
            {STEP_CHOICES.map(choice => (
                <TouchableOpacity
                    key={choice.value}
                    style={[
                        styles.choiceButton,
                        selectedStep === choice.value && styles.selectedChoice
                    ]}
                    onPress={() => setSelectedStep(choice.value)}
                >
                    <Text style={styles.choiceText}>{choice.label}</Text>
                </TouchableOpacity>
            ))}

            <View style={[commonStyles.buttonContainer, styles.saveButton]}>
                {isLoading ? <ActivityIndicator /> : (
                    <Button title="确认添加" onPress={handleSave} disabled={!selectedStep} />
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    choiceButton: {
        width: '90%',
        padding: 20,
        marginVertical: 10,
        backgroundColor: '#f0f0f0',
        borderRadius: 10,
        borderWidth: 2,
        borderColor: 'transparent',
    },
    selectedChoice: {
        borderColor: '#007AFF',
        backgroundColor: '#e6f2ff',
    },
    choiceText: {
        fontSize: 18,
        textAlign: 'center',
    },
    saveButton: {
        marginTop: 40,
    }
});
