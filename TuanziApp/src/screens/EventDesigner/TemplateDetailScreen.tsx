import React, { useState, useCallback } from 'react';
import { View, Text, FlatList, Button, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useRoute, RouteProp, useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { getTemplateDetails } from '../../api/eventApi';
import { EventTemplate, RootStackParamList, EventStep } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type TemplateDetailRouteProp = RouteProp<RootStackParamList, 'TemplateDetail'>;
type NavigationProp = StackNavigationProp<RootStackParamList, 'TemplateDetail'>;

export const TemplateDetailScreen = () => {
    const route = useRoute<TemplateDetailRouteProp>();
    const navigation = useNavigation<NavigationProp>();
    const { templateId } = route.params;
    const { token } = useAuth();

    const [template, setTemplate] = useState<EventTemplate | null>(null);
    const [isLoading, setIsLoading] = useState(true);

    const fetchDetails = useCallback(async () => {
        if (!token) return;
        try {
            setIsLoading(true);
            const data = await getTemplateDetails(token, templateId);
            setTemplate(data);
        } catch (error) {
            Alert.alert('错误', '无法加载模板详情。');
        } finally {
            setIsLoading(false);
        }
    }, [token, templateId]);

    useFocusEffect(useCallback(() => { fetchDetails(); }, [fetchDetails]));

    const renderStepItem = ({ item }: { item: EventStep }) => (
        <View style={styles.stepCard}>
            <Text style={styles.stepOrder}>第 {item.order} 步</Text>
            <Text style={styles.stepType}>{item.step_type.replace(/_/g, ' ')}</Text>
            <Text style={styles.stepDuration}>{item.duration / 60} 分钟</Text>
        </View>
    );

    if (isLoading) {
        return <View style={commonStyles.container}><ActivityIndicator size="large" /></View>;
    }

    if (!template) {
        return <View style={commonStyles.container}><Text>未找到模板。</Text></View>;
    }

    return (
        <View style={styles.container}>
            <FlatList
                data={template.steps}
                renderItem={renderStepItem}
                keyExtractor={(item) => item.id.toString()}
                ListHeaderComponent={
                    <View style={styles.header}>
                        <Text style={commonStyles.title}>{template.name}</Text>
                        <Text style={styles.description}>{template.description}</Text>
                    </View>
                }
                ListEmptyComponent={
                    <Text style={styles.emptyText}>这个模板还没有任何步骤。</Text>
                }
            />
            <View style={styles.buttonContainer}>
                <Button title="添加新步骤" onPress={() => navigation.navigate('AddStep', { templateId })} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1 },
    header: { padding: 20, borderBottomWidth: 1, borderColor: '#eee' },
    description: { fontSize: 16, color: '#666', marginTop: 10, textAlign: 'center' },
    buttonContainer: { padding: 20 },
    stepCard: {
        backgroundColor: '#fff',
        padding: 15,
        marginVertical: 8,
        marginHorizontal: 16,
        borderRadius: 10,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        elevation: 2,
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 1.41,
    },
    stepOrder: { fontSize: 16, fontWeight: 'bold' },
    stepType: { fontSize: 16, color: '#333' },
    stepDuration: { fontSize: 14, color: '#888' },
    emptyText: { textAlign: 'center', marginTop: 50, fontSize: 16, color: '#888' },
});
