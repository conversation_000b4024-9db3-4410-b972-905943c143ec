import React, { useState, useCallback } from 'react';
import { View, Text, FlatList, Button, StyleSheet, Alert, ActivityIndicator, TouchableOpacity } from 'react-native';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { getEventTemplates } from '../../api/eventApi';
import { EventTemplate, RootStackParamList } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type NavigationProp = StackNavigationProp<RootStackParamList, 'EventDesigner'>;

export const TemplateListScreen = () => {
    const { token } = useAuth();
    const navigation = useNavigation<NavigationProp>();
    const [templates, setTemplates] = useState<EventTemplate[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    const fetchTemplates = useCallback(async () => {
        if (!token) return;
        try {
            setIsLoading(true);
            const data = await getEventTemplates(token);
            setTemplates(data);
        } catch (error) {
            console.error(error);
            Alert.alert('错误', '无法加载环节模板。');
        } finally {
            setIsLoading(false);
        }
    }, [token]);

    useFocusEffect(useCallback(() => { fetchTemplates(); }, [fetchTemplates]));

    const renderItem = ({ item }: { item: EventTemplate }) => (
        <TouchableOpacity 
            style={styles.templateCard}
            onPress={() => navigation.navigate('TemplateDetail', { templateId: item.id })}
        >
            <Text style={styles.templateName}>{item.name}</Text>
            <Text style={styles.templateDescription}>{item.description}</Text>
        </TouchableOpacity>
    );

    if (isLoading) {
        return <View style={commonStyles.container}><ActivityIndicator size="large" /></View>;
    }

    return (
        <View style={styles.container}>
            <FlatList
                data={templates}
                renderItem={renderItem}
                keyExtractor={(item) => item.id.toString()}
                ListHeaderComponent={
                    <Text style={commonStyles.title}>我的环节模板</Text>
                }
                ListEmptyComponent={
                    <Text style={styles.emptyText}>您还没有创建任何模板。</Text>
                }
                contentContainerStyle={styles.listContainer}
            />
            <View style={styles.buttonContainer}>
                <Button title="创建新模板" onPress={() => navigation.navigate('CreateTemplate')} />
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1 },
    listContainer: { paddingBottom: 80 },
    buttonContainer: { position: 'absolute', bottom: 20, left: 20, right: 20 },
    templateCard: { backgroundColor: '#f9f9f9', padding: 20, marginVertical: 8, marginHorizontal: 16, borderRadius: 10, borderWidth: 1, borderColor: '#eee' },
    templateName: { fontSize: 18, fontWeight: 'bold' },
    templateDescription: { fontSize: 14, color: '#666', marginTop: 5 },
    emptyText: { textAlign: 'center', marginTop: 50, fontSize: 16, color: '#888' },
});
