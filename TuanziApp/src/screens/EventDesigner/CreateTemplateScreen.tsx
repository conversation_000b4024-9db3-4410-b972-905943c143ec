import React, { useState } from 'react';
import { View, Text, TextInput, Button, StyleSheet, Alert, ActivityIndicator } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { useAuth } from '../../auth/AuthContext';
import { createEventTemplate } from '../../api/eventApi';
import { RootStackParamList } from '../../types';
import { commonStyles } from '../../styles/commonStyles';

type NavigationProp = StackNavigationProp<RootStackParamList, 'CreateTemplate'>;

export const CreateTemplateScreen = () => {
    const { token } = useAuth();
    const navigation = useNavigation<NavigationProp>();
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [isLoading, setIsLoading] = useState(false);

    const handleSave = async () => {
        if (!name) {
            Alert.alert('错误', '模板名称不能为空。');
            return;
        }
        if (!token) {
            Alert.alert('错误', '认证失败，请重新登录。');
            return;
        }

        setIsLoading(true);
        try {
            await createEventTemplate(token, name, description);
            Alert.alert('成功', '新模板已创建！');
            navigation.goBack();
        } catch (error) {
            console.error(error);
            Alert.alert('错误', '创建模板失败。');
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <View style={commonStyles.container}>
            <Text style={commonStyles.subtitle}>模板名称</Text>
            <TextInput
                style={commonStyles.input}
                placeholder="例如：公司年会流程"
                value={name}
                onChangeText={setName}
            />

            <Text style={commonStyles.subtitle}>模板描述</Text>
            <TextInput
                style={[commonStyles.input, styles.descriptionInput]}
                placeholder="描述这个模板的用途..."
                value={description}
                onChangeText={setDescription}
                multiline
            />

            <View style={commonStyles.buttonContainer}>
                {isLoading ? (
                    <ActivityIndicator size="large" />
                ) : (
                    <Button title="保存模板" onPress={handleSave} />
                )}
            </View>
        </View>
    );
};

const styles = StyleSheet.create({
    descriptionInput: {
        height: 100,
        textAlignVertical: 'top',
        paddingTop: 10,
    },
});
