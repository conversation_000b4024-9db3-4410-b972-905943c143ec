package com.tuanziapp

import android.os.Bundle;
import com.facebook.react.ReactActivity
import com.facebook.react.ReactActivityDelegate
import com.facebook.react.defaults.DefaultNewArchitectureEntryPoint
import com.facebook.react.defaults.DefaultReactActivityDelegate

class MainActivity : ReactActivity() {

  /**
   * Returns the name of the main component registered from JavaScript. This is used to schedule
   * rendering of the component.
   */
  override fun getMainComponentName(): String = "TuanziApp"

  /**
   * Returns the instance of the [ReactActivityDelegate]. We use [DefaultReactActivityDelegate]
   * which allows you to easily enable Fabric and Concurrent React (React 18) with two boolean
   * flags.
   */
  override fun createReactActivityDelegate(): ReactActivityDelegate =
      DefaultReactActivityDelegate(
          this,
          mainComponentName,
          // If you opted-in for the New Architecture, we enable the Fabric Renderer.
          DefaultNewArchitectureEntryPoint.fabricEnabled,
          // If you opted-in for the New Architecture, we enable Concurrent React (i.e. React 18).
          DefaultNewArchitectureEntryPoint.concurrentReactEnabled
      )

  /**
   * This override is required for an issue with react-native-screens.
   * It should be added to your MainActivity.kt file.
   */
  override fun onCreate(savedInstanceState: Bundle?) {
    super.onCreate(null)
  }
}
