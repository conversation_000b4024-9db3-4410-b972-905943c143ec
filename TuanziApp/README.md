# Tuanzi 移动应用 (React Native)

## 1. 概述

本项目前端使用React Native和TypeScript构建，通过`React Navigation`进行页面导航，并采用基于Context的全局状态管理模式处理用户认证。

## 2. 环境设置

**前置要求**:
-   Node.js (LTS版本) & npm
-   React Native开发环境 (请参考官方文档设置 "React Native CLI Quickstart")
-   Android Studio (用于获取Android SDK和模拟器)
-   `adb` (安卓调试桥)

**安装步骤**:

1.  **克隆仓库**

2.  **安装NPM依赖**:
    ```bash
    cd TuanziApp
    npm install
    ```

3.  **配置原生依赖 (仅首次需要)**:
    本项目依赖`react-native-gesture-handler`和`react-native-svg`。请确保已按照它们的官方文档完成了所有安卓原生部分的配置（特别是`MainActivity.kt`和`index.js`的修改）。

## 3. 运行开发环境

1.  **配置IP地址 (关键步骤)**:
    在开始前，运行以下命令。它会自动检测您电脑的局域网IP，并将其配置到项目中，以便手机可以连接到后端服务。
    ```bash
    npm run config:ip
    ```

2.  **启动开发服务器**:
    在一个终端中，运行以下命令来同时启动后端和Metro：
    ```bash
    npm run dev
    ```

3.  **编译并运行App**:
    在**另一个**终端中，运行：
    ```bash
    npm run android
    ```

## 4. 项目架构 (`src`目录)

-   `api/`: 存放所有与后端API通信的函数 (`eventApi.ts`) 和配置文件 (`client.ts`, `config.ts`)。
-   `auth/`: 负责用户认证。
    -   `AuthContext.tsx`: 全局的用户状态管理器。
    -   `storage.ts`: 封装了与`AsyncStorage`的交互，用于持久化存储token。
-   `components/`: 存放可复用的UI组件。
    -   `PictionaryCanvas.tsx`: “你画我猜”的画布组件。
    -   `steps/`: 存放每个环节对应的UI组件，如`PictionaryView.tsx`, `LobbyView.tsx`。
-   `navigation/`: 负责所有页面导航。
    -   `index.tsx`: `RootNavigator`，根据用户登录状态决定显示认证流程还是主应用。
    -   `AppNavigator.tsx`: 主应用内的页面栈。
    -   `AuthNavigator.tsx`: 登录/注册页面的栈。
-   `screens/`: 存放每个独立的页面级组件。
    -   `EventDesigner/`: 存放与“环节设计器”相关的页面。
-   `styles/`: 存放全局和模块化的样式表。
-   `types.ts`: 全局TypeScript类型定义，是保证类型安全的“真相之源”。

## 5. 核心逻辑：状态管理

-   **认证**: `AuthContext`负责全局的用户登录/登出状态。`RootNavigator`根据`AuthContext`中的`user`对象来决定渲染哪个导航器。
-   **房间与环节**: `RoomScreen.tsx`是房间内的核心状态管理器。它负责监听所有WebSocket消息，并根据收到的当前环节类型 (`currentStep.step_type`)，动态地渲染对应的子组件（如`PictionaryView`），并将所有需要的数据作为props传递下去。这种单向数据流模式保证了UI的稳定和可预测性。
