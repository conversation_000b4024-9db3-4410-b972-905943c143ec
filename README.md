# 团子 (Tuanzi) - 团建活动集成APP

*我们团，你们玩*

---

## 1. 项目简介

**团子**是一个集成的手机APP，旨在解决团队活动（如公司团建、班级活动、朋友聚会）中可能遇到的所有事务。其核心是高度可定制的**“环节” (Events/Flow) 系统**，允许主持人像编排剧本一样，将不同的游戏、讨论、投票等活动组合成一个完整的、可复用的团建流程。

## 2. 项目状态 (截至 2025年7月4日)

**当前阶段**: **阶段二：功能拓展** (初期)

我们已经完成了MVP的开发和第一阶段的架构重构。应用目前拥有一个稳定、可扩展的框架，并成功集成了一个完整的核心玩法。

-   **已完成**:
    -   ✅ **核心架构**: 基于Django Channels和React Native的实时通信架构。
    -   ✅ **用户系统**: 支持用户注册和基于JWT的登录认证。
    -   ✅ **房间系统**: 支持创建房间、加入房间，并以“环节”为核心驱动流程。
    -   ✅ **环节设计器 (V1)**: 支持创建环节模板、添加环节步骤（你画我猜/自由聊天）、删除步骤。
    -   ✅ **首个游戏集成**: “你画我猜”已作为第一个环节类型被完整集成，包含绘图、猜词、计分、多轮游戏等核心逻辑。

-   **已知问题 / 下一步工作**:
    -   **[BUG]** 在新结构中，你画我猜的绘画者不能正常显示需要绘画的词条，取而代之的是与猜测者一样的下划线，而且倒计时系统失灵。
    -   **[BUG]** 在新结构中，你画我猜结束一轮猜测之后会卡死在白屏界面。
    -   **[TODO]** 完善环节设计器，增加步骤的编辑和拖拽排序功能。
    -   **[TODO]** 开发更多环节类型，如“谁是卧底”、投票、问答等。

## 3. 技术栈

-   **后端**: Python, Django, Django REST Framework, Django Channels, Daphne (ASGI Server)
-   **前端**: React, React Native, TypeScript
-   **核心库**: React Navigation, react-native-svg, react-native-gesture-handler
-   **数据库**: SQLite (开发), PostgreSQL (生产)

## 4. 快速开始 (一站式启动)

为了简化开发流程，我们配置了自动化脚本。

1.  **配置IP地址 (仅在更换网络时需要)**:
    ```bash
    cd TuanziApp
    npm run config:ip
    ```

2.  **启动所有服务**:
    在一个终端窗口中，运行以下命令来同时启动后端服务器和前端Metro打包器。
    ```bash
    cd TuanziApp
    npm run dev
    ```

3.  **编译并运行App**:
    在**另一个**终端窗口中，运行：
    ```bash
    cd TuanziApp
    npm run android
    ```

## 5. 详细文档

-   [后端设置与架构 (Tuanzi_Backend/README.md)](./Tuanzi_Backend/README.md)
-   [前端设置与架构 (TuanziApp/README.md)](./TuanziApp/README.md)
-   [模块扩展指南 (CONTRIBUTING.md)](./CONTRIBUTING.md)

## 6. 开发团队

-   **开发者A (gellar)**: 负责项目架构、核心后端功能。
-   **开发者B**: 负责用户界面、核心游戏功能。
-   **设计师**: 提供美术意见和UI/UX指导。
